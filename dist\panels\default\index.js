"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const fs_extra_1 = require("fs-extra");
const path_1 = require("path");
const vue_1 = require("vue");
const weakMap = new WeakMap();
// UUID檢查的輔助函數
class UuidChecker {
    constructor() {
        this.projectPath = '';
        this.allUuids = new Map();
        this.uuidReferences = new Map();
        this.projectPath = Editor.Project.path;
    }
    // 獲取所有資源信息
    async getAllAssets() {
        try {
            const assets = await Editor.Message.request('asset-db', 'query-assets', {
                pattern: "db://assets/**/**",
            });
            return assets.map(asset => ({
                uuid: asset.uuid,
                path: asset.file,
                name: asset.displayName || asset.name,
                type: asset.type
            }));
        }
        catch (error) {
            console.error('獲取資源失敗:', error);
            return [];
        }
    }
    // 搜尋檔案中的UUID引用
    async searchUuidReferences(uuids) {
        const references = new Map();
        const searchExtensions = ['.scene', '.prefab', '.ts', '.js', '.json', '.meta'];
        // 初始化所有UUID的引用列表
        uuids.forEach(uuid => references.set(uuid, []));
        try {
            await this.searchInDirectory(this.projectPath, uuids, references, searchExtensions);
        }
        catch (error) {
            console.error('搜尋UUID引用失敗:', error);
        }
        return references;
    }
    // 遞歸搜尋目錄
    async searchInDirectory(dirPath, uuids, references, searchExtensions) {
        try {
            const items = (0, fs_extra_1.readdirSync)(dirPath);
            for (const item of items) {
                const fullPath = (0, path_1.join)(dirPath, item);
                const stat = (0, fs_extra_1.statSync)(fullPath);
                if (stat.isDirectory()) {
                    // 跳過一些不需要搜尋的目錄
                    if (!['node_modules', '.git', 'library', 'local', 'temp'].includes(item)) {
                        await this.searchInDirectory(fullPath, uuids, references, searchExtensions);
                    }
                }
                else if (stat.isFile()) {
                    const ext = (0, path_1.extname)(fullPath).toLowerCase();
                    if (searchExtensions.includes(ext)) {
                        await this.searchInFile(fullPath, uuids, references);
                    }
                }
            }
        }
        catch (error) {
            console.error(`搜尋目錄失敗 ${dirPath}:`, error);
        }
    }
    // 搜尋單個檔案
    async searchInFile(filePath, uuids, references) {
        try {
            const content = (0, fs_extra_1.readFileSync)(filePath, 'utf-8');
            const relativePath = (0, path_1.relative)(this.projectPath, filePath);
            uuids.forEach(uuid => {
                if (content.includes(uuid)) {
                    const refs = references.get(uuid) || [];
                    refs.push(relativePath);
                    references.set(uuid, refs);
                }
            });
        }
        catch (error) {
            // 忽略無法讀取的檔案
        }
    }
    // 執行完整的UUID檢查
    async performCheck(progressCallback) {
        try {
            // 步驟1: 獲取所有資源
            progressCallback === null || progressCallback === void 0 ? void 0 : progressCallback(10, '正在獲取專案資源...');
            const assets = await this.getAllAssets();
            if (assets.length === 0) {
                throw new Error('未找到任何資源');
            }
            // 步驟2: 搜尋UUID引用
            progressCallback === null || progressCallback === void 0 ? void 0 : progressCallback(30, '正在搜尋UUID引用...');
            const uuids = assets.map(asset => asset.uuid);
            const references = await this.searchUuidReferences(uuids);
            // 步驟3: 分類已使用和未使用的UUID
            progressCallback === null || progressCallback === void 0 ? void 0 : progressCallback(80, '正在分析結果...');
            const usedUuids = [];
            const unusedUuids = [];
            assets.forEach(asset => {
                const refs = references.get(asset.uuid) || [];
                if (refs.length > 0) {
                    usedUuids.push(Object.assign(Object.assign({}, asset), { references: refs }));
                }
                else {
                    unusedUuids.push(asset);
                }
            });
            progressCallback === null || progressCallback === void 0 ? void 0 : progressCallback(100, '檢查完成');
            return {
                totalFiles: assets.length,
                usedUuids,
                unusedUuids
            };
        }
        catch (error) {
            console.error('UUID檢查失敗:', error);
            throw error;
        }
    }
}
/**
 * @zh 如果希望兼容 3.3 之前的版本可以使用下方的代码
 * @en You can add the code below if you want compatibility with versions prior to 3.3
 */
// Editor.Panel.define = Editor.Panel.define || function(options: any) { return options }
module.exports = Editor.Panel.define({
    listeners: {
        show() { console.log('show'); },
        hide() { console.log('hide'); },
    },
    template: (0, fs_extra_1.readFileSync)((0, path_1.join)(__dirname, '../../../static/template/default/index.html'), 'utf-8'),
    style: (0, fs_extra_1.readFileSync)((0, path_1.join)(__dirname, '../../../static/style/default/index.css'), 'utf-8'),
    $: {
        app: '#app',
        text: '#text',
    },
    methods: {
        hello() {
            if (this.$.text) {
                this.$.text.innerHTML = 'hello';
                console.log('[cocos-panel-html.default]: hello');
            }
        },
    },
    ready() {
        if (this.$.text) {
            this.$.text.innerHTML = 'Hello Cocos.';
        }
        if (this.$.app) {
            const app = (0, vue_1.createApp)({});
            app.config.compilerOptions.isCustomElement = (tag) => tag.startsWith('ui-');
            // UUID檢查器組件
            app.component('UuidChecker', {
                template: (0, fs_extra_1.readFileSync)((0, path_1.join)(__dirname, '../../../static/template/vue/uuid-checker.html'), 'utf-8'),
                data() {
                    return {
                        isChecking: false,
                        progress: 0,
                        progressText: '',
                        checkResult: null,
                        error: null,
                        showUsedList: false
                    };
                },
                methods: {
                    async startCheck() {
                        this.isChecking = true;
                        this.progress = 0;
                        this.progressText = '開始檢查...';
                        this.checkResult = null;
                        this.error = null;
                        try {
                            const checker = new UuidChecker();
                            const result = await checker.performCheck((progress, text) => {
                                this.progress = progress;
                                this.progressText = text;
                            });
                            this.checkResult = result;
                        }
                        catch (error) {
                            console.error('UUID檢查失敗:', error);
                            this.error = error instanceof Error ? error.message : '檢查過程中發生未知錯誤';
                        }
                        finally {
                            this.isChecking = false;
                        }
                    },
                    toggleUsedList() {
                        this.showUsedList = !this.showUsedList;
                    }
                }
            });
            app.mount(this.$.app);
            weakMap.set(this, app);
        }
    },
    beforeClose() { },
    close() {
        const app = weakMap.get(this);
        if (app) {
            app.unmount();
        }
    },
});
