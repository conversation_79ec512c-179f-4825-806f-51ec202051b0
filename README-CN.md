# UUID檢查工具

一個基於 Vue3.x 開發的Cocos Creator擴展工具，用於檢查專案中UUID的使用情況。該工具可以掃描專案中的所有檔案，找出未被使用的UUID資源，幫助開發者清理無用的資源檔案。

## 功能特色

- **全面掃描**: 掃描專案中的所有資源檔案，獲取完整的UUID列表
- **智能檢查**: 搜尋 .scene、.prefab、.ts、.js、.json、.meta 等檔案中的UUID引用
- **詳細報告**: 顯示已使用和未使用的UUID，包含檔案路徑和引用位置
- **即時進度**: 檢查過程中顯示即時進度和狀態
- **友好界面**: 清晰的結果展示，支援展開/收合詳細信息

## 开发环境

Node.js

## 安装

```bash
# 安装依赖模块
npm install
# 构建
npm run build
```

## 用法

1. **啟用擴展**: 在Cocos Creator中啟用此擴展
2. **打開面板**: 點擊主菜單欄中的 `面板 -> uuid_tools -> UUID工具面板`
3. **開始檢查**: 在打開的面板中點擊「開始檢查」按鈕
4. **查看結果**: 檢查完成後會顯示詳細的結果報告

### 檢查結果說明

- **總檔案數**: 專案中掃描到的資源檔案總數
- **已使用的UUID**: 在專案中被其他檔案引用的UUID列表
- **未使用的UUID**: 沒有被任何檔案引用的UUID列表（可能是無用的資源）

### 結果詳情

每個UUID項目會顯示：
- UUID值
- 檔案路徑
- 資源名稱和類型
- 引用位置（僅限已使用的UUID）

## 注意事項

- 檢查過程可能需要一些時間，取決於專案大小
- 工具會跳過 node_modules、.git、library、local、temp 等系統目錄
- 建議在刪除未使用的資源前先備份專案
