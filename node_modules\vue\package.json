{"name": "vue", "version": "3.1.4", "description": "The progressive JavaScript framework for buiding modern web UI.", "main": "index.js", "module": "dist/vue.runtime.esm-bundler.js", "types": "dist/vue.d.ts", "unpkg": "dist/vue.global.js", "jsdelivr": "dist/vue.global.js", "files": ["index.js", "dist"], "buildOptions": {"name": "<PERSON><PERSON>", "formats": ["esm-bundler", "esm-bundler-runtime", "cjs", "global", "global-runtime", "esm-browser", "esm-browser-runtime"]}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-next.git"}, "keywords": ["vue"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vue-next/issues"}, "homepage": "https://github.com/vuejs/vue-next/tree/master/packages/vue#readme", "dependencies": {"@vue/shared": "3.1.4", "@vue/compiler-dom": "3.1.4", "@vue/runtime-dom": "3.1.4"}, "devDependencies": {"lodash": "^4.17.15", "marked": "^0.7.0", "todomvc-app-css": "^2.3.0"}}