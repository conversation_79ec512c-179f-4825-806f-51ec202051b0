# UUID Checker Tool

A Cocos Creator extension tool based on Vue3.x for checking UUID usage in projects. This tool can scan all files in the project, find unused UUID resources, and help developers clean up unnecessary resource files.

## Features

- **Comprehensive Scanning**: Scans all resource files in the project to get a complete UUID list
- **Smart Checking**: Searches for UUID references in .scene, .prefab, .ts, .js, .json, .meta files
- **Detailed Reports**: Shows used and unused UUIDs with file paths and reference locations
- **Real-time Progress**: Displays real-time progress and status during checking
- **User-friendly Interface**: Clear result display with expandable/collapsible details

## Development Environment

Node.js

## Install

```bash
# Install dependent modules
npm install
# build
npm run build
```

## Usage

1. **Enable Extension**: Enable this extension in Cocos Creator
2. **Open Panel**: Click `Panel -> uuid_tools -> UUID Tools Panel` in the main menu bar
3. **Start Checking**: Click the "Start Check" button in the opened panel
4. **View Results**: After checking is complete, detailed result reports will be displayed

### Check Results Explanation

- **Total Files**: Total number of resource files scanned in the project
- **Used UUIDs**: List of UUIDs that are referenced by other files in the project
- **Unused UUIDs**: List of UUIDs that are not referenced by any files (potentially useless resources)

### Result Details

Each UUID item displays:
- UUID value
- File path
- Resource name and type
- Reference locations (for used UUIDs only)

## Notes

- The checking process may take some time depending on project size
- The tool skips system directories like node_modules, .git, library, local, temp
- It's recommended to backup your project before deleting unused resources
