# 故障排除指南

## 常見問題

### 1. 面板無法載入 - entities 包錯誤

**錯誤信息**: `Package subpath './lib/decode' is not defined by "exports" in entities/package.json`

**解決方案**:
這個問題是由於Vue版本過新導致的依賴衝突。我們已經將Vue版本鎖定為3.1.4來解決這個問題。

如果仍然遇到這個問題：

1. 刪除 `node_modules` 目錄
2. 刪除 `package-lock.json` 檔案
3. 執行 `npm install --legacy-peer-deps`
4. 執行 `npm run build`

### 2. 檢查功能無法正常工作

**可能原因**:
- 專案路徑無法訪問
- 權限問題
- 資源數據庫未就緒

**解決方案**:
1. 確保Cocos Creator專案已完全載入
2. 檢查控制台是否有錯誤信息
3. 嘗試重新啟動Cocos Creator

### 3. 進度顯示異常

**可能原因**:
- Vue組件渲染問題
- CSS樣式衝突

**解決方案**:
1. 檢查瀏覽器開發者工具的控制台
2. 確保CSS檔案正確載入
3. 重新編譯專案

### 4. 搜尋結果不準確

**可能原因**:
- 動態載入的資源
- 字符串拼接的UUID引用
- 註釋中的UUID

**注意事項**:
- 工具只能檢測靜態的UUID引用
- 動態生成的UUID引用可能無法檢測到
- 建議手動驗證重要資源

## 版本兼容性

- **Cocos Creator**: >= 3.6.2
- **Node.js**: >= 16.0.0
- **Vue**: 3.1.4 (已鎖定)
- **TypeScript**: ^4.3.4

## 調試步驟

1. **檢查編譯**:
   ```bash
   npm run build
   ```

2. **檢查依賴**:
   ```bash
   npm list
   ```

3. **清理重建**:
   ```bash
   rm -rf node_modules package-lock.json
   npm install --legacy-peer-deps
   npm run build
   ```

4. **檢查日誌**:
   - 打開Cocos Creator控制台
   - 查看擴展載入日誌
   - 檢查面板載入錯誤

## 聯繫支援

如果問題仍然存在，請提供：
- Cocos Creator版本
- 錯誤信息截圖
- 控制台日誌
- 專案結構概述
