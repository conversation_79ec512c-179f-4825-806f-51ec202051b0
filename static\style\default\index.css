#text {
    color: var(--color-normal-contrast-weakest);
    margin: auto;
    width: 180px;
}
.counter {
    text-align: center;
}

.uuid-checker {
    padding: 20px;
    max-height: 100vh;
    overflow-y: auto;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 1px solid var(--color-normal-contrast-weakest);
    padding-bottom: 10px;
}

.header h2 {
    margin: 0;
    color: var(--color-normal-contrast-emphasis);
}

.check-btn {
    min-width: 120px;
}

.progress {
    margin: 20px 0;
    padding: 15px;
    background: var(--color-normal-contrast-weakest);
    border-radius: 4px;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background-color: var(--color-normal-contrast-weakest);
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background-color: var(--color-success-fill);
    transition: width 0.3s ease;
    border-radius: 10px;
}

.progress p {
    margin: 10px 0 0 0;
    color: var(--color-normal-contrast-emphasis);
    text-align: center;
}

.results {
    margin-top: 20px;
}

.summary {
    background: var(--color-normal-contrast-weakest);
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
}

.summary h3 {
    margin: 0 0 10px 0;
    color: var(--color-normal-contrast-emphasis);
}

.summary p {
    margin: 5px 0;
    color: var(--color-normal-contrast-emphasis);
}

.unused-section, .used-section {
    margin: 20px 0;
    border: 1px solid var(--color-normal-contrast-weakest);
    border-radius: 4px;
    padding: 15px;
}

.unused-section h3, .used-section h3 {
    margin: 0 0 15px 0;
    color: var(--color-normal-contrast-emphasis);
}

.toggle-btn {
    margin-bottom: 15px;
}

.unused-list, .used-list {
    max-height: 400px;
    overflow-y: auto;
}

.unused-item, .used-item {
    background: var(--color-normal-contrast-weakest);
    margin: 10px 0;
    padding: 12px;
    border-radius: 4px;
    border-left: 4px solid var(--color-warn-fill);
}

.used-item {
    border-left-color: var(--color-success-fill);
}

.uuid-info, .file-info, .asset-info {
    margin: 5px 0;
    font-size: 12px;
    color: var(--color-normal-contrast-emphasis);
}

.uuid-info strong, .file-info strong, .asset-info strong {
    color: var(--color-normal-contrast-emphasis);
}

.asset-type {
    color: var(--color-normal-contrast-weak);
    font-style: italic;
}

.references {
    margin: 10px 0 0 0;
    font-size: 12px;
}

.references ul {
    margin: 5px 0 0 20px;
    padding: 0;
}

.references li {
    margin: 2px 0;
    color: var(--color-normal-contrast-weak);
    font-family: monospace;
    font-size: 11px;
}

.error {
    background: var(--color-error-fill);
    color: var(--color-error-contrast);
    padding: 15px;
    border-radius: 4px;
    margin: 20px 0;
}

.error h3 {
    margin: 0 0 10px 0;
}

.error p {
    margin: 0;
}