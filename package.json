{"package_version": 2, "version": "1.0.0", "name": "uuid_tools", "description": "i18n:uuid_tools.description", "main": "./dist/main.js", "dependencies": {"vue": "^3.1.4", "fs-extra": "^10.0.0"}, "devDependencies": {"@types/node": "^16.0.1", "@types/fs-extra": "^9.0.5", "typescript": "^4.3.4"}, "panels": {"default": {"title": "uuid_tools Default Panel", "type": "dockable", "main": "dist/panels/default", "size": {"min-width": 400, "min-height": 300, "width": 1024, "height": 600}}}, "contributions": {"menu": [{"path": "i18n:menu.panel/uuid_tools", "label": "i18n:uuid_tools.open_panel", "message": "open-panel"}], "messages": {"open-panel": {"methods": ["openPanel"]}}}, "author": "Cocos Creator", "editor": ">=3.6.2", "scripts": {"build": "tsc -b", "watch": "tsc -w"}}