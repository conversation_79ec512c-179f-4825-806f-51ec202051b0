var VueCompilerDOM=function(e){"use strict";function t(e,t){const n=Object.create(null),o=e.split(",");for(let r=0;r<o.length;r++)n[o[r]]=!0;return t?e=>!!n[e.toLowerCase()]:e=>!!n[e]}const n=/;(?![^(]*\))/g,o=/:(.+)/;const r=t("html,body,base,head,link,meta,style,title,address,article,aside,footer,header,h1,h2,h3,h4,h5,h6,hgroup,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,rtc,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot"),s=t("svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistanceLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view"),i=t("area,base,br,col,embed,hr,img,input,link,meta,param,source,track,wbr"),c={},l=()=>{},a=()=>!1,p=/^on[^a-z]/,u=Object.assign,f=Array.isArray,d=e=>"string"==typeof e,h=e=>"symbol"==typeof e,m=e=>null!==e&&"object"==typeof e,g=t(",key,ref,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),y=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},v=/-(\w)/g,b=y((e=>e.replace(v,((e,t)=>t?t.toUpperCase():"")))),S=/\B([A-Z])/g,E=y((e=>e.replace(S,"-$1").toLowerCase())),T=y((e=>e.charAt(0).toUpperCase()+e.slice(1))),x=y((e=>e?`on${T(e)}`:""));function _(e){throw e}function N(e){}function O(e,t,n,o){const r=new SyntaxError(String(e));return r.code=e,r.loc=t,r}const C=Symbol(""),I=Symbol(""),k=Symbol(""),M=Symbol(""),P=Symbol(""),w=Symbol(""),R=Symbol(""),$=Symbol(""),L=Symbol(""),V=Symbol(""),A=Symbol(""),D=Symbol(""),B=Symbol(""),F=Symbol(""),j=Symbol(""),H=Symbol(""),W=Symbol(""),K=Symbol(""),U=Symbol(""),J=Symbol(""),G=Symbol(""),z=Symbol(""),q=Symbol(""),Y=Symbol(""),Z=Symbol(""),X=Symbol(""),Q=Symbol(""),ee=Symbol(""),te=Symbol(""),ne=Symbol(""),oe=Symbol(""),re=Symbol(""),se={[C]:"Fragment",[I]:"Teleport",[k]:"Suspense",[M]:"KeepAlive",[P]:"BaseTransition",[w]:"openBlock",[R]:"createBlock",[$]:"createVNode",[L]:"createCommentVNode",[V]:"createTextVNode",[A]:"createStaticVNode",[D]:"resolveComponent",[B]:"resolveDynamicComponent",[F]:"resolveDirective",[j]:"resolveFilter",[H]:"withDirectives",[W]:"renderList",[K]:"renderSlot",[U]:"createSlots",[J]:"toDisplayString",[G]:"mergeProps",[z]:"toHandlers",[q]:"camelize",[Y]:"capitalize",[Z]:"toHandlerKey",[X]:"setBlockTracking",[Q]:"pushScopeId",[ee]:"popScopeId",[te]:"withScopeId",[ne]:"withCtx",[oe]:"unref",[re]:"isRef"};function ie(e){Object.getOwnPropertySymbols(e).forEach((t=>{se[t]=e[t]}))}const ce={source:"",start:{line:1,column:1,offset:0},end:{line:1,column:1,offset:0}};function le(e,t=ce){return{type:0,children:e,helpers:[],components:[],directives:[],hoists:[],imports:[],cached:0,temps:0,codegenNode:void 0,loc:t}}function ae(e,t,n,o,r,s,i,c=!1,l=!1,a=ce){return e&&(c?(e.helper(w),e.helper(R)):e.helper($),i&&e.helper(H)),{type:13,tag:t,props:n,children:o,patchFlag:r,dynamicProps:s,directives:i,isBlock:c,disableTracking:l,loc:a}}function pe(e,t=ce){return{type:17,loc:t,elements:e}}function ue(e,t=ce){return{type:15,loc:t,properties:e}}function fe(e,t){return{type:16,loc:ce,key:d(e)?de(e,!0):e,value:t}}function de(e,t,n=ce,o=0){return{type:4,loc:n,content:e,isStatic:t,constType:t?3:o}}function he(e,t=ce){return{type:8,loc:t,children:e}}function me(e,t=[],n=ce){return{type:14,loc:n,callee:e,arguments:t}}function ge(e,t,n=!1,o=!1,r=ce){return{type:18,params:e,returns:t,newline:n,isSlot:o,loc:r}}function ye(e,t,n,o=!0){return{type:19,test:e,consequent:t,alternate:n,newline:o,loc:ce}}function ve(e,t,n=!1){return{type:20,index:e,value:t,isVNode:n,loc:ce}}const be=e=>4===e.type&&e.isStatic,Se=(e,t)=>e===t||e===E(t);function Ee(e){return Se(e,"Teleport")?I:Se(e,"Suspense")?k:Se(e,"KeepAlive")?M:Se(e,"BaseTransition")?P:void 0}const Te=/^\d|[^\$\w]/,xe=e=>!Te.test(e),_e=/[A-Za-z_$\xA0-\uFFFF]/,Ne=/[\.\w$\xA0-\uFFFF]/,Oe=/\s+[.[]\s*|\s*[.[]\s+/g,Ce=e=>{e=e.trim().replace(Oe,(e=>e.trim()));let t=0,n=0,o=0,r=null;for(let s=0;s<e.length;s++){const i=e.charAt(s);switch(t){case 0:if("["===i)n=t,t=1,o++;else if(!(0===s?_e:Ne).test(i))return!1;break;case 1:"'"===i||'"'===i||"`"===i?(n=t,t=2,r=i):"["===i?o++:"]"===i&&(--o||(t=n));break;case 2:i===r&&(t=n,r=null)}}return!o};function Ie(e,t,n){const o={source:e.source.substr(t,n),start:ke(e.start,e.source,t),end:e.end};return null!=n&&(o.end=ke(e.start,e.source,t+n)),o}function ke(e,t,n=t.length){return Me(u({},e),t,n)}function Me(e,t,n=t.length){let o=0,r=-1;for(let s=0;s<n;s++)10===t.charCodeAt(s)&&(o++,r=s);return e.offset+=n,e.line+=o,e.column=-1===r?e.column+n:n-r,e}function Pe(e,t,n=!1){for(let o=0;o<e.props.length;o++){const r=e.props[o];if(7===r.type&&(n||r.exp)&&(d(t)?r.name===t:t.test(r.name)))return r}}function we(e,t,n=!1,o=!1){for(let r=0;r<e.props.length;r++){const s=e.props[r];if(6===s.type){if(n)continue;if(s.name===t&&(s.value||o))return s}else if("bind"===s.name&&(s.exp||o)&&Re(s.arg,t))return s}}function Re(e,t){return!(!e||!be(e)||e.content!==t)}function $e(e){return e.props.some((e=>!(7!==e.type||"bind"!==e.name||e.arg&&4===e.arg.type&&e.arg.isStatic)))}function Le(e){return 5===e.type||2===e.type}function Ve(e){return 7===e.type&&"slot"===e.name}function Ae(e){return 1===e.type&&3===e.tagType}function De(e){return 1===e.type&&2===e.tagType}function Be(e,t,n){let o;const r=13===e.type?e.props:e.arguments[2];if(null==r||d(r))o=ue([t]);else if(14===r.type){const e=r.arguments[0];d(e)||15!==e.type?r.callee===z?o=me(n.helper(G),[ue([t]),r]):r.arguments.unshift(ue([t])):e.properties.unshift(t),!o&&(o=r)}else if(15===r.type){let e=!1;if(4===t.key.type){const n=t.key.content;e=r.properties.some((e=>4===e.key.type&&e.key.content===n))}e||r.properties.unshift(t),o=r}else o=me(n.helper(G),[ue([t]),r]);13===e.type?e.props=o:e.arguments[2]=o}function Fe(e,t){return`_${t}_${e.replace(/[^\w]/g,"_")}`}const je={COMPILER_IS_ON_ELEMENT:{message:'Platform-native elements with "is" prop will no longer be treated as components in Vue 3 unless the "is" value is explicitly prefixed with "vue:".',link:"https://v3.vuejs.org/guide/migration/custom-elements-interop.html"},COMPILER_V_BIND_SYNC:{message:e=>`.sync modifier for v-bind has been removed. Use v-model with argument instead. \`v-bind:${e}.sync\` should be changed to \`v-model:${e}\`.`,link:"https://v3.vuejs.org/guide/migration/v-model.html"},COMPILER_V_BIND_PROP:{message:".prop modifier for v-bind has been removed and no longer necessary. Vue 3 will automatically set a binding as DOM property when appropriate."},COMPILER_V_BIND_OBJECT_ORDER:{message:'v-bind="obj" usage is now order sensitive and behaves like JavaScript object spread: it will now overwrite an existing non-mergeable attribute that appears before v-bind in the case of conflict. To retain 2.x behavior, move v-bind to make it the first attribute. You can also suppress this warning if the usage is intended.',link:"https://v3.vuejs.org/guide/migration/v-bind.html"},COMPILER_V_ON_NATIVE:{message:".native modifier for v-on has been removed as is no longer necessary.",link:"https://v3.vuejs.org/guide/migration/v-on-native-modifier-removed.html"},COMPILER_V_IF_V_FOR_PRECEDENCE:{message:"v-if / v-for precedence when used on the same element has changed in Vue 3: v-if now takes higher precedence and will no longer have access to v-for scope variables. It is best to avoid the ambiguity with <template> tags or use a computed property that filters v-for data source.",link:"https://v3.vuejs.org/guide/migration/v-if-v-for.html"},COMPILER_V_FOR_REF:{message:"Ref usage on v-for no longer creates array ref values in Vue 3. Consider using function refs or refactor to avoid ref usage altogether.",link:"https://v3.vuejs.org/guide/migration/array-refs.html"},COMPILER_NATIVE_TEMPLATE:{message:"<template> with no special directives will render as a native template element instead of its inner content in Vue 3."},COMPILER_INLINE_TEMPLATE:{message:'"inline-template" has been removed in Vue 3.',link:"https://v3.vuejs.org/guide/migration/inline-template-attribute.html"},COMPILER_FILTER:{message:'filters have been removed in Vue 3. The "|" symbol will be treated as native JavaScript bitwise OR operator. Use method calls or computed properties instead.',link:"https://v3.vuejs.org/guide/migration/filters.html"}};function He(e,t){const n=t.options?t.options.compatConfig:t.compatConfig,o=n&&n[e];return"MODE"===e?o||3:o}function We(e,t){const n=He("MODE",t),o=He(e,t);return 3===n?!0===o:!1!==o}function Ke(e,t,n,...o){return We(e,t)}const Ue=/&(gt|lt|amp|apos|quot);/g,Je={gt:">",lt:"<",amp:"&",apos:"'",quot:'"'},Ge={delimiters:["{{","}}"],getNamespace:()=>0,getTextMode:()=>0,isVoidTag:a,isPreTag:a,isCustomElement:a,decodeEntities:e=>e.replace(Ue,((e,t)=>Je[t])),onError:_,onWarn:N,comments:!1};function ze(e,t={}){const n=function(e,t){const n=u({},Ge);for(const o in t)n[o]=t[o]||Ge[o];return{options:n,column:1,line:1,offset:0,originalSource:e,source:e,inPre:!1,inVPre:!1,onWarn:n.onWarn}}(e,t),o=lt(n);return le(qe(n,0,[]),at(n,o))}function qe(e,t,n){const o=pt(n),r=o?o.ns:0,s=[];for(;!mt(e,t,n);){const i=e.source;let c;if(0===t||1===t)if(!e.inVPre&&ut(i,e.options.delimiters[0]))c=st(e,t);else if(0===t&&"<"===i[0])if(1===i.length);else if("!"===i[1])c=ut(i,"\x3c!--")?Xe(e):ut(i,"<!DOCTYPE")?Qe(e):ut(i,"<![CDATA[")&&0!==r?Ze(e,n):Qe(e);else if("/"===i[1])if(2===i.length);else{if(">"===i[2]){ft(e,3);continue}if(/[a-z]/i.test(i[2])){nt(e,1,o);continue}c=Qe(e)}else/[a-z]/i.test(i[1])?(c=et(e,n),We("COMPILER_NATIVE_TEMPLATE",e)&&c&&"template"===c.tag&&!c.props.some((e=>7===e.type&&tt(e.name)))&&(c=c.children)):"?"===i[1]&&(c=Qe(e));if(c||(c=it(e,t)),f(c))for(let e=0;e<c.length;e++)Ye(s,c[e]);else Ye(s,c)}let i=!1;if(2!==t&&1!==t){const t="preserve"===e.options.whitespace;for(let n=0;n<s.length;n++){const o=s[n];if(!e.inPre&&2===o.type)if(/[^\t\r\n\f ]/.test(o.content))t||(o.content=o.content.replace(/[\t\r\n\f ]+/g," "));else{const e=s[n-1],r=s[n+1];!e||!r||!t&&(3===e.type||3===r.type||1===e.type&&1===r.type&&/[\r\n]/.test(o.content))?(i=!0,s[n]=null):o.content=" "}3!==o.type||e.options.comments||(i=!0,s[n]=null)}if(e.inPre&&o&&e.options.isPreTag(o.tag)){const e=s[0];e&&2===e.type&&(e.content=e.content.replace(/^\r?\n/,""))}}return i?s.filter(Boolean):s}function Ye(e,t){if(2===t.type){const n=pt(e);if(n&&2===n.type&&n.loc.end.offset===t.loc.start.offset)return n.content+=t.content,n.loc.end=t.loc.end,void(n.loc.source+=t.loc.source)}e.push(t)}function Ze(e,t){ft(e,9);const n=qe(e,3,t);return 0===e.source.length||ft(e,3),n}function Xe(e){const t=lt(e);let n;const o=/--(\!)?>/.exec(e.source);if(o){n=e.source.slice(4,o.index);const t=e.source.slice(0,o.index);let r=1,s=0;for(;-1!==(s=t.indexOf("\x3c!--",r));)ft(e,s-r+1),r=s+1;ft(e,o.index+o[0].length-r+1)}else n=e.source.slice(4),ft(e,e.source.length);return{type:3,content:n,loc:at(e,t)}}function Qe(e){const t=lt(e),n="?"===e.source[1]?1:2;let o;const r=e.source.indexOf(">");return-1===r?(o=e.source.slice(n),ft(e,e.source.length)):(o=e.source.slice(n,r),ft(e,r+1)),{type:3,content:o,loc:at(e,t)}}function et(e,t){const n=e.inPre,o=e.inVPre,r=pt(t),s=nt(e,0,r),i=e.inPre&&!n,c=e.inVPre&&!o;if(s.isSelfClosing||e.options.isVoidTag(s.tag))return e.options.isPreTag(s.tag)&&(e.inPre=!1),s;t.push(s);const l=e.options.getTextMode(s,r),a=qe(e,l,t);t.pop();{const t=s.props.find((e=>6===e.type&&"inline-template"===e.name));if(t&&Ke("COMPILER_INLINE_TEMPLATE",e)){const n=at(e,s.loc.end);t.value={type:2,content:n.source,loc:n}}}if(s.children=a,gt(e.source,s.tag))nt(e,1,r);else if(0===e.source.length&&"script"===s.tag.toLowerCase()){const e=a[0];e&&ut(e.loc.source,"\x3c!--")}return s.loc=at(e,s.loc.start),i&&(e.inPre=!1),c&&(e.inVPre=!1),s}const tt=t("if,else,else-if,for,slot");function nt(e,t,n){const o=lt(e),r=/^<\/?([a-z][^\t\r\n\f />]*)/i.exec(e.source),s=r[1],i=e.options.getNamespace(s,n);ft(e,r[0].length),dt(e);const c=lt(e),l=e.source;e.options.isPreTag(s)&&(e.inPre=!0);let a=ot(e,t);0===t&&!e.inVPre&&a.some((e=>7===e.type&&"pre"===e.name))&&(e.inVPre=!0,u(e,c),e.source=l,a=ot(e,t).filter((e=>"v-pre"!==e.name)));let p=!1;if(0===e.source.length||(p=ut(e.source,"/>"),ft(e,p?2:1)),1===t)return;let f=0;return e.inVPre||("slot"===s?f=2:"template"===s?a.some((e=>7===e.type&&tt(e.name)))&&(f=3):function(e,t,n){const o=n.options;if(o.isCustomElement(e))return!1;if("component"===e||/^[A-Z]/.test(e)||Ee(e)||o.isBuiltInComponent&&o.isBuiltInComponent(e)||o.isNativeTag&&!o.isNativeTag(e))return!0;for(let r=0;r<t.length;r++){const e=t[r];if(6===e.type){if("is"===e.name&&e.value){if(e.value.content.startsWith("vue:"))return!0;if(Ke("COMPILER_IS_ON_ELEMENT",n))return!0}}else{if("is"===e.name)return!0;if("bind"===e.name&&Re(e.arg,"is")&&Ke("COMPILER_IS_ON_ELEMENT",n))return!0}}}(s,a,e)&&(f=1)),{type:1,ns:i,tag:s,tagType:f,props:a,isSelfClosing:p,children:[],loc:at(e,o),codegenNode:void 0}}function ot(e,t){const n=[],o=new Set;for(;e.source.length>0&&!ut(e.source,">")&&!ut(e.source,"/>");){if(ut(e.source,"/")){ft(e,1),dt(e);continue}const r=rt(e,o);0===t&&n.push(r),/^[^\t\r\n\f />]/.test(e.source),dt(e)}return n}function rt(e,t){const n=lt(e),o=/^[^\t\r\n\f />][^\t\r\n\f />=]*/.exec(e.source)[0];t.has(o),t.add(o);{const e=/["'<]/g;let t;for(;t=e.exec(o););}let r;ft(e,o.length),/^[\t\r\n\f ]*=/.test(e.source)&&(dt(e),ft(e,1),dt(e),r=function(e){const t=lt(e);let n;const o=e.source[0],r='"'===o||"'"===o;if(r){ft(e,1);const t=e.source.indexOf(o);-1===t?n=ct(e,e.source.length,4):(n=ct(e,t,4),ft(e,1))}else{const t=/^[^\t\r\n\f >]+/.exec(e.source);if(!t)return;const o=/["'<=`]/g;let r;for(;r=o.exec(t[0]););n=ct(e,t[0].length,4)}return{content:n,isQuoted:r,loc:at(e,t)}}(e));const s=at(e,n);if(!e.inVPre&&/^(v-|:|@|#)/.test(o)){const t=/(?:^v-([a-z0-9-]+))?(?:(?::|^@|^#)(\[[^\]]+\]|[^\.]+))?(.+)?$/i.exec(o);let i,c=t[1]||(ut(o,":")?"bind":ut(o,"@")?"on":"slot");if(t[2]){const r="slot"===c,s=o.lastIndexOf(t[2]),l=at(e,ht(e,n,s),ht(e,n,s+t[2].length+(r&&t[3]||"").length));let a=t[2],p=!0;a.startsWith("[")?(p=!1,a.endsWith("]"),a=a.substr(1,a.length-2)):r&&(a+=t[3]||""),i={type:4,content:a,isStatic:p,constType:p?3:0,loc:l}}if(r&&r.isQuoted){const e=r.loc;e.start.offset++,e.start.column++,e.end=ke(e.start,r.content),e.source=e.source.slice(1,-1)}const l=t[3]?t[3].substr(1).split("."):[];return"bind"===c&&i&&l.includes("sync")&&Ke("COMPILER_V_BIND_SYNC",e,0)&&(c="model",l.splice(l.indexOf("sync"),1)),{type:7,name:c,exp:r&&{type:4,content:r.content,isStatic:!1,constType:0,loc:r.loc},arg:i,modifiers:l,loc:s}}return{type:6,name:o,value:r&&{type:2,content:r.content,loc:r.loc},loc:s}}function st(e,t){const[n,o]=e.options.delimiters,r=e.source.indexOf(o,n.length);if(-1===r)return;const s=lt(e);ft(e,n.length);const i=lt(e),c=lt(e),l=r-n.length,a=e.source.slice(0,l),p=ct(e,l,t),u=p.trim(),f=p.indexOf(u);f>0&&Me(i,a,f);return Me(c,a,l-(p.length-u.length-f)),ft(e,o.length),{type:5,content:{type:4,isStatic:!1,constType:0,content:u,loc:at(e,i,c)},loc:at(e,s)}}function it(e,t){const n=["<",e.options.delimiters[0]];3===t&&n.push("]]>");let o=e.source.length;for(let s=0;s<n.length;s++){const t=e.source.indexOf(n[s],1);-1!==t&&o>t&&(o=t)}const r=lt(e);return{type:2,content:ct(e,o,t),loc:at(e,r)}}function ct(e,t,n){const o=e.source.slice(0,t);return ft(e,t),2===n||3===n||-1===o.indexOf("&")?o:e.options.decodeEntities(o,4===n)}function lt(e){const{column:t,line:n,offset:o}=e;return{column:t,line:n,offset:o}}function at(e,t,n){return{start:t,end:n=n||lt(e),source:e.originalSource.slice(t.offset,n.offset)}}function pt(e){return e[e.length-1]}function ut(e,t){return e.startsWith(t)}function ft(e,t){const{source:n}=e;Me(e,n,t),e.source=n.slice(t)}function dt(e){const t=/^[\t\r\n\f ]+/.exec(e.source);t&&ft(e,t[0].length)}function ht(e,t,n){return ke(t,e.originalSource.slice(t.offset,n),n)}function mt(e,t,n){const o=e.source;switch(t){case 0:if(ut(o,"</"))for(let e=n.length-1;e>=0;--e)if(gt(o,n[e].tag))return!0;break;case 1:case 2:{const e=pt(n);if(e&&gt(o,e.tag))return!0;break}case 3:if(ut(o,"]]>"))return!0}return!o}function gt(e,t){return ut(e,"</")&&e.substr(2,t.length).toLowerCase()===t.toLowerCase()&&/[\t\r\n\f />]/.test(e[2+t.length]||">")}function yt(e,t){bt(e,t,vt(e,e.children[0]))}function vt(e,t){const{children:n}=e;return 1===n.length&&1===t.type&&!De(t)}function bt(e,t,n=!1){let o=!1,r=!0;const{children:s}=e;for(let i=0;i<s.length;i++){const e=s[i];if(1===e.type&&0===e.tagType){const s=n?0:St(e,t);if(s>0){if(s<3&&(r=!1),s>=2){e.codegenNode.patchFlag="-1",e.codegenNode=t.hoist(e.codegenNode),o=!0;continue}}else{const n=e.codegenNode;if(13===n.type){const o=xt(n);if((!o||512===o||1===o)&&Et(e,t)>=2){const o=Tt(e);o&&(n.props=t.hoist(o))}}}}else if(12===e.type){const n=St(e.content,t);n>0&&(n<3&&(r=!1),n>=2&&(e.codegenNode=t.hoist(e.codegenNode),o=!0))}if(1===e.type){const n=1===e.tagType;n&&t.scopes.vSlot++,bt(e,t),n&&t.scopes.vSlot--}else if(11===e.type)bt(e,t,1===e.children.length);else if(9===e.type)for(let n=0;n<e.branches.length;n++)bt(e.branches[n],t,1===e.branches[n].children.length)}r&&o&&t.transformHoist&&t.transformHoist(s,t,e)}function St(e,t){const{constantCache:n}=t;switch(e.type){case 1:if(0!==e.tagType)return 0;const o=n.get(e);if(void 0!==o)return o;const r=e.codegenNode;if(13!==r.type)return 0;if(xt(r))return n.set(e,0),0;{let o=3;const s=Et(e,t);if(0===s)return n.set(e,0),0;s<o&&(o=s);for(let r=0;r<e.children.length;r++){const s=St(e.children[r],t);if(0===s)return n.set(e,0),0;s<o&&(o=s)}if(o>1)for(let r=0;r<e.props.length;r++){const s=e.props[r];if(7===s.type&&"bind"===s.name&&s.exp){const r=St(s.exp,t);if(0===r)return n.set(e,0),0;r<o&&(o=r)}}return r.isBlock&&(t.removeHelper(w),t.removeHelper(R),r.isBlock=!1,t.helper($)),n.set(e,o),o}case 2:case 3:return 3;case 9:case 11:case 10:return 0;case 5:case 12:return St(e.content,t);case 4:return e.constType;case 8:let s=3;for(let n=0;n<e.children.length;n++){const o=e.children[n];if(d(o)||h(o))continue;const r=St(o,t);if(0===r)return 0;r<s&&(s=r)}return s;default:return 0}}function Et(e,t){let n=3;const o=Tt(e);if(o&&15===o.type){const{properties:e}=o;for(let o=0;o<e.length;o++){const{key:r,value:s}=e[o],i=St(r,t);if(0===i)return i;if(i<n&&(n=i),4!==s.type)return 0;const c=St(s,t);if(0===c)return c;c<n&&(n=c)}}return n}function Tt(e){const t=e.codegenNode;if(13===t.type)return t.props}function xt(e){const t=e.patchFlag;return t?parseInt(t,10):void 0}function _t(e,{filename:t="",prefixIdentifiers:n=!1,hoistStatic:o=!1,cacheHandlers:r=!1,nodeTransforms:s=[],directiveTransforms:i={},transformHoist:a=null,isBuiltInComponent:p=l,isCustomElement:u=l,expressionPlugins:f=[],scopeId:d=null,slotted:h=!0,ssr:m=!1,ssrCssVars:g="",bindingMetadata:y=c,inline:v=!1,isTS:S=!1,onError:E=_,onWarn:x=N,compatConfig:O}){const C=t.replace(/\?.*$/,"").match(/([^/\\]+)\.\w+$/),I={selfName:C&&T(b(C[1])),prefixIdentifiers:n,hoistStatic:o,cacheHandlers:r,nodeTransforms:s,directiveTransforms:i,transformHoist:a,isBuiltInComponent:p,isCustomElement:u,expressionPlugins:f,scopeId:d,slotted:h,ssr:m,ssrCssVars:g,bindingMetadata:y,inline:v,isTS:S,onError:E,onWarn:x,compatConfig:O,root:e,helpers:new Map,components:new Set,directives:new Set,hoists:[],imports:[],constantCache:new Map,temps:0,cached:0,identifiers:Object.create(null),scopes:{vFor:0,vSlot:0,vPre:0,vOnce:0},parent:null,currentNode:e,childIndex:0,helper(e){const t=I.helpers.get(e)||0;return I.helpers.set(e,t+1),e},removeHelper(e){const t=I.helpers.get(e);if(t){const n=t-1;n?I.helpers.set(e,n):I.helpers.delete(e)}},helperString:e=>`_${se[I.helper(e)]}`,replaceNode(e){I.parent.children[I.childIndex]=I.currentNode=e},removeNode(e){const t=e?I.parent.children.indexOf(e):I.currentNode?I.childIndex:-1;e&&e!==I.currentNode?I.childIndex>t&&(I.childIndex--,I.onNodeRemoved()):(I.currentNode=null,I.onNodeRemoved()),I.parent.children.splice(t,1)},onNodeRemoved:()=>{},addIdentifiers(e){},removeIdentifiers(e){},hoist(e){I.hoists.push(e);const t=de(`_hoisted_${I.hoists.length}`,!1,e.loc,2);return t.hoisted=e,t},cache:(e,t=!1)=>ve(++I.cached,e,t)};return I.filters=new Set,I}function Nt(e,t){const n=_t(e,t);Ot(e,n),t.hoistStatic&&yt(e,n),t.ssr||function(e,t){const{helper:n,removeHelper:o}=t,{children:r}=e;if(1===r.length){const t=r[0];if(vt(e,t)&&t.codegenNode){const r=t.codegenNode;13===r.type&&(r.isBlock||(o($),r.isBlock=!0,n(w),n(R))),e.codegenNode=r}else e.codegenNode=t}else if(r.length>1){let o=64;e.codegenNode=ae(t,n(C),void 0,e.children,o+"",void 0,void 0,!0)}}(e,n),e.helpers=[...n.helpers.keys()],e.components=[...n.components],e.directives=[...n.directives],e.imports=n.imports,e.hoists=n.hoists,e.temps=n.temps,e.cached=n.cached,e.filters=[...n.filters]}function Ot(e,t){t.currentNode=e;const{nodeTransforms:n}=t,o=[];for(let s=0;s<n.length;s++){const r=n[s](e,t);if(r&&(f(r)?o.push(...r):o.push(r)),!t.currentNode)return;e=t.currentNode}switch(e.type){case 3:t.ssr||t.helper(L);break;case 5:t.ssr||t.helper(J);break;case 9:for(let n=0;n<e.branches.length;n++)Ot(e.branches[n],t);break;case 10:case 11:case 1:case 0:!function(e,t){let n=0;const o=()=>{n--};for(;n<e.children.length;n++){const r=e.children[n];d(r)||(t.parent=e,t.childIndex=n,t.onNodeRemoved=o,Ot(r,t))}}(e,t)}t.currentNode=e;let r=o.length;for(;r--;)o[r]()}function Ct(e,t){const n=d(e)?t=>t===e:t=>e.test(t);return(e,o)=>{if(1===e.type){const{props:r}=e;if(3===e.tagType&&r.some(Ve))return;const s=[];for(let i=0;i<r.length;i++){const c=r[i];if(7===c.type&&n(c.name)){r.splice(i,1),i--;const n=t(e,c,o);n&&s.push(n)}}return s}}}const It="/*#__PURE__*/";function kt(e,t={}){const n=function(e,{mode:t="function",prefixIdentifiers:n="module"===t,sourceMap:o=!1,filename:r="template.vue.html",scopeId:s=null,optimizeImports:i=!1,runtimeGlobalName:c="Vue",runtimeModuleName:l="vue",ssr:a=!1,isTS:p=!1}){const u={mode:t,prefixIdentifiers:n,sourceMap:o,filename:r,scopeId:s,optimizeImports:i,runtimeGlobalName:c,runtimeModuleName:l,ssr:a,isTS:p,source:e.loc.source,code:"",column:1,line:1,offset:0,indentLevel:0,pure:!1,map:void 0,helper:e=>`_${se[e]}`,push(e,t){u.code+=e},indent(){f(++u.indentLevel)},deindent(e=!1){e?--u.indentLevel:f(--u.indentLevel)},newline(){f(u.indentLevel)}};function f(e){u.push("\n"+"  ".repeat(e))}return u}(e,t);t.onContextCreated&&t.onContextCreated(n);const{mode:o,push:r,prefixIdentifiers:s,indent:i,deindent:c,newline:l,ssr:a}=n,p=e.helpers.length>0,u=!s&&"module"!==o;!function(e,t){const{push:n,newline:o,runtimeGlobalName:r}=t,s=r,i=e=>`${se[e]}: _${se[e]}`;if(e.helpers.length>0&&(n(`const _Vue = ${s}\n`),e.hoists.length)){n(`const { ${[$,L,V,A].filter((t=>e.helpers.includes(t))).map(i).join(", ")} } = _Vue\n`)}(function(e,t){if(!e.length)return;t.pure=!0;const{push:n,newline:o}=t;o(),e.forEach(((e,r)=>{e&&(n(`const _hoisted_${r+1} = `),Rt(e,t),o())})),t.pure=!1})(e.hoists,t),o(),n("return ")}(e,n);if(r(`function ${a?"ssrRender":"render"}(${(a?["_ctx","_push","_parent","_attrs"]:["_ctx","_cache"]).join(", ")}) {`),i(),u&&(r("with (_ctx) {"),i(),p&&(r(`const { ${e.helpers.map((e=>`${se[e]}: _${se[e]}`)).join(", ")} } = _Vue`),r("\n"),l())),e.components.length&&(Mt(e.components,"component",n),(e.directives.length||e.temps>0)&&l()),e.directives.length&&(Mt(e.directives,"directive",n),e.temps>0&&l()),e.filters&&e.filters.length&&(l(),Mt(e.filters,"filter",n),l()),e.temps>0){r("let ");for(let t=0;t<e.temps;t++)r(`${t>0?", ":""}_temp${t}`)}return(e.components.length||e.directives.length||e.temps)&&(r("\n"),l()),a||r("return "),e.codegenNode?Rt(e.codegenNode,n):r("null"),u&&(c(),r("}")),c(),r("}"),{ast:e,code:n.code,preamble:"",map:n.map?n.map.toJSON():void 0}}function Mt(e,t,{helper:n,push:o,newline:r,isTS:s}){const i=n("filter"===t?j:"component"===t?D:F);for(let c=0;c<e.length;c++){let n=e[c];const l=n.endsWith("__self");l&&(n=n.slice(0,-6)),o(`const ${Fe(n,t)} = ${i}(${JSON.stringify(n)}${l?", true":""})${s?"!":""}`),c<e.length-1&&r()}}function Pt(e,t){const n=e.length>3||!1;t.push("["),n&&t.indent(),wt(e,t,n),n&&t.deindent(),t.push("]")}function wt(e,t,n=!1,o=!0){const{push:r,newline:s}=t;for(let i=0;i<e.length;i++){const c=e[i];d(c)?r(c):f(c)?Pt(c,t):Rt(c,t),i<e.length-1&&(n?(o&&r(","),s()):o&&r(", "))}}function Rt(e,t){if(d(e))t.push(e);else if(h(e))t.push(t.helper(e));else switch(e.type){case 1:case 9:case 11:Rt(e.codegenNode,t);break;case 2:!function(e,t){t.push(JSON.stringify(e.content),e)}(e,t);break;case 4:$t(e,t);break;case 5:!function(e,t){const{push:n,helper:o,pure:r}=t;r&&n(It);n(`${o(J)}(`),Rt(e.content,t),n(")")}(e,t);break;case 12:Rt(e.codegenNode,t);break;case 8:Lt(e,t);break;case 3:!function(e,t){const{push:n,helper:o,pure:r}=t;r&&n(It);n(`${o(L)}(${JSON.stringify(e.content)})`,e)}(e,t);break;case 13:!function(e,t){const{push:n,helper:o,pure:r}=t,{tag:s,props:i,children:c,patchFlag:l,dynamicProps:a,directives:p,isBlock:u,disableTracking:f}=e;p&&n(o(H)+"(");u&&n(`(${o(w)}(${f?"true":""}), `);r&&n(It);n(o(u?R:$)+"(",e),wt(function(e){let t=e.length;for(;t--&&null==e[t];);return e.slice(0,t+1).map((e=>e||"null"))}([s,i,c,l,a]),t),n(")"),u&&n(")");p&&(n(", "),Rt(p,t),n(")"))}(e,t);break;case 14:!function(e,t){const{push:n,helper:o,pure:r}=t,s=d(e.callee)?e.callee:o(e.callee);r&&n(It);n(s+"(",e),wt(e.arguments,t),n(")")}(e,t);break;case 15:!function(e,t){const{push:n,indent:o,deindent:r,newline:s}=t,{properties:i}=e;if(!i.length)return void n("{}",e);const c=i.length>1||!1;n(c?"{":"{ "),c&&o();for(let l=0;l<i.length;l++){const{key:e,value:o}=i[l];Vt(e,t),n(": "),Rt(o,t),l<i.length-1&&(n(","),s())}c&&r(),n(c?"}":" }")}(e,t);break;case 17:!function(e,t){Pt(e.elements,t)}(e,t);break;case 18:!function(e,t){const{push:n,indent:o,deindent:r}=t,{params:s,returns:i,body:c,newline:l,isSlot:a}=e;a&&n(`_${se[ne]}(`);n("(",e),f(s)?wt(s,t):s&&Rt(s,t);n(") => "),(l||c)&&(n("{"),o());i?(l&&n("return "),f(i)?Pt(i,t):Rt(i,t)):c&&Rt(c,t);(l||c)&&(r(),n("}"));a&&(e.isNonScopedSlot&&n(", undefined, true"),n(")"))}(e,t);break;case 19:!function(e,t){const{test:n,consequent:o,alternate:r,newline:s}=e,{push:i,indent:c,deindent:l,newline:a}=t;if(4===n.type){const e=!xe(n.content);e&&i("("),$t(n,t),e&&i(")")}else i("("),Rt(n,t),i(")");s&&c(),t.indentLevel++,s||i(" "),i("? "),Rt(o,t),t.indentLevel--,s&&a(),s||i(" "),i(": ");const p=19===r.type;p||t.indentLevel++;Rt(r,t),p||t.indentLevel--;s&&l(!0)}(e,t);break;case 20:!function(e,t){const{push:n,helper:o,indent:r,deindent:s,newline:i}=t;n(`_cache[${e.index}] || (`),e.isVNode&&(r(),n(`${o(X)}(-1),`),i());n(`_cache[${e.index}] = `),Rt(e.value,t),e.isVNode&&(n(","),i(),n(`${o(X)}(1),`),i(),n(`_cache[${e.index}]`),s());n(")")}(e,t)}}function $t(e,t){const{content:n,isStatic:o}=e;t.push(o?JSON.stringify(n):n,e)}function Lt(e,t){for(let n=0;n<e.children.length;n++){const o=e.children[n];d(o)?t.push(o):Rt(o,t)}}function Vt(e,t){const{push:n}=t;if(8===e.type)n("["),Lt(e,t),n("]");else if(e.isStatic){n(xe(e.content)?e.content:JSON.stringify(e.content),e)}else n(`[${e.content}]`,e)}function At(e,t,n=!1,o=!1){return e}const Dt=Ct(/^(if|else|else-if)$/,((e,t,n)=>Bt(e,t,n,((e,t,o)=>{const r=n.parent.children;let s=r.indexOf(e),i=0;for(;s-- >=0;){const e=r[s];e&&9===e.type&&(i+=e.branches.length)}return()=>{if(o)e.codegenNode=jt(t,i,n);else{(function(e){for(;;)if(19===e.type){if(19!==e.alternate.type)return e;e=e.alternate}else 20===e.type&&(e=e.value)}(e.codegenNode)).alternate=jt(t,i+e.branches.length-1,n)}}}))));function Bt(e,t,n,o){if(!("else"===t.name||t.exp&&t.exp.content.trim())){t.exp=de("true",!1,t.exp?t.exp.loc:e.loc)}if("if"===t.name){const r=Ft(e,t),s={type:9,loc:e.loc,branches:[r]};if(n.replaceNode(s),o)return o(s,r,!0)}else{const r=n.parent.children;let s=r.indexOf(e);for(;s-- >=-1;){const i=r[s];if(!i||2!==i.type||i.content.trim().length){if(i&&9===i.type){n.removeNode();const r=Ft(e,t);i.branches.push(r);const s=o&&o(i,r,!1);Ot(r,n),s&&s(),n.currentNode=null}break}n.removeNode(i)}}}function Ft(e,t){return{type:10,loc:e.loc,condition:"else"===t.name?void 0:t.exp,children:3!==e.tagType||Pe(e,"for")?[e]:e.children,userKey:we(e,"key")}}function jt(e,t,n){return e.condition?ye(e.condition,Ht(e,t,n),me(n.helper(L),['""',"true"])):Ht(e,t,n)}function Ht(e,t,n){const{helper:o,removeHelper:r}=n,s=fe("key",de(`${t}`,!1,ce,2)),{children:i}=e,c=i[0];if(1!==i.length||1!==c.type){if(1===i.length&&11===c.type){const e=c.codegenNode;return Be(e,s,n),e}{let t=64;return ae(n,o(C),ue([s]),i,t+"",void 0,void 0,!0,!1,e.loc)}}{const e=c.codegenNode;return 13!==e.type||e.isBlock||(r($),e.isBlock=!0,o(w),o(R)),Be(e,s,n),e}}const Wt=Ct("for",((e,t,n)=>{const{helper:o,removeHelper:r}=n;return Kt(e,t,n,(t=>{const s=me(o(W),[t.source]),i=we(e,"key"),c=i?fe("key",6===i.type?de(i.value.content,!0):i.exp):null,l=4===t.source.type&&t.source.constType>0,a=l?64:i?128:256;return t.codegenNode=ae(n,o(C),void 0,s,a+"",void 0,void 0,!0,!l,e.loc),()=>{let i;const a=Ae(e),{children:p}=t,u=1!==p.length||1!==p[0].type,f=De(e)?e:a&&1===e.children.length&&De(e.children[0])?e.children[0]:null;f?(i=f.codegenNode,a&&c&&Be(i,c,n)):u?i=ae(n,o(C),c?ue([c]):void 0,e.children,"64",void 0,void 0,!0):(i=p[0].codegenNode,a&&c&&Be(i,c,n),i.isBlock!==!l&&(i.isBlock?(r(w),r(R)):r($)),i.isBlock=!l,i.isBlock?(o(w),o(R)):o($)),s.arguments.push(ge(Yt(t.parseResult),i,!0))}}))}));function Kt(e,t,n,o){if(!t.exp)return;const r=zt(t.exp);if(!r)return;const{scopes:s}=n,{source:i,value:c,key:l,index:a}=r,p={type:11,loc:t.loc,source:i,valueAlias:c,keyAlias:l,objectIndexAlias:a,parseResult:r,children:Ae(e)?e.children:[e]};n.replaceNode(p),s.vFor++;const u=o&&o(p);return()=>{s.vFor--,u&&u()}}const Ut=/([\s\S]*?)\s+(?:in|of)\s+([\s\S]*)/,Jt=/,([^,\}\]]*)(?:,([^,\}\]]*))?$/,Gt=/^\(|\)$/g;function zt(e,t){const n=e.loc,o=e.content,r=o.match(Ut);if(!r)return;const[,s,i]=r,c={source:qt(n,i.trim(),o.indexOf(i,s.length)),value:void 0,key:void 0,index:void 0};let l=s.trim().replace(Gt,"").trim();const a=s.indexOf(l),p=l.match(Jt);if(p){l=l.replace(Jt,"").trim();const e=p[1].trim();let t;if(e&&(t=o.indexOf(e,a+l.length),c.key=qt(n,e,t)),p[2]){const r=p[2].trim();r&&(c.index=qt(n,r,o.indexOf(r,c.key?t+e.length:a+l.length)))}}return l&&(c.value=qt(n,l,a)),c}function qt(e,t,n){return de(t,!1,Ie(e,n,t.length))}function Yt({value:e,key:t,index:n}){const o=[];return e&&o.push(e),t&&(e||o.push(de("_",!1)),o.push(t)),n&&(t||(e||o.push(de("_",!1)),o.push(de("__",!1))),o.push(n)),o}const Zt=de("undefined",!1),Xt=(e,t)=>{if(1===e.type&&(1===e.tagType||3===e.tagType)){const n=Pe(e,"slot");if(n)return t.scopes.vSlot++,()=>{t.scopes.vSlot--}}},Qt=(e,t,n)=>ge(e,t,!1,!0,t.length?t[0].loc:n);function en(e,t,n=Qt){t.helper(ne);const{children:o,loc:r}=e,s=[],i=[];let c=t.scopes.vSlot>0||t.scopes.vFor>0;const l=Pe(e,"slot",!0);if(l){const{arg:e,exp:t}=l;e&&!be(e)&&(c=!0),s.push(fe(e||de("default",!0),n(t,o,r)))}let a=!1,p=!1;const u=[],f=new Set;for(let m=0;m<o.length;m++){const e=o[m];let r;if(!Ae(e)||!(r=Pe(e,"slot",!0))){3!==e.type&&u.push(e);continue}if(l)break;a=!0;const{children:d,loc:h}=e,{arg:g=de("default",!0),exp:y}=r;let v;be(g)?v=g?g.content:"default":c=!0;const b=n(y,d,h);let S,E,T;if(S=Pe(e,"if"))c=!0,i.push(ye(S.exp,tn(g,b),Zt));else if(E=Pe(e,/^else(-if)?$/,!0)){let e,t=m;for(;t--&&(e=o[t],3===e.type););if(e&&Ae(e)&&Pe(e,"if")){o.splice(m,1),m--;let e=i[i.length-1];for(;19===e.alternate.type;)e=e.alternate;e.alternate=E.exp?ye(E.exp,tn(g,b),Zt):tn(g,b)}}else if(T=Pe(e,"for")){c=!0;const e=T.parseResult||zt(T.exp);e&&i.push(me(t.helper(W),[e.source,ge(Yt(e),tn(g,b),!0)]))}else{if(v){if(f.has(v))continue;f.add(v),"default"===v&&(p=!0)}s.push(fe(g,b))}}if(!l){const e=(e,o)=>{const s=n(e,o,r);return t.compatConfig&&(s.isNonScopedSlot=!0),fe("default",s)};a?u.length&&u.some((e=>on(e)))&&(p||s.push(e(void 0,u))):s.push(e(void 0,o))}const d=c?2:nn(e.children)?3:1;let h=ue(s.concat(fe("_",de(d+"",!1))),r);return i.length&&(h=me(t.helper(U),[h,pe(i)])),{slots:h,hasDynamicSlots:c}}function tn(e,t){return ue([fe("name",e),fe("fn",t)])}function nn(e){for(let t=0;t<e.length;t++){const n=e[t];switch(n.type){case 1:if(2===n.tagType||0===n.tagType&&nn(n.children))return!0;break;case 9:if(nn(n.branches))return!0;break;case 10:case 11:if(nn(n.children))return!0}}return!1}function on(e){return 2!==e.type&&12!==e.type||(2===e.type?!!e.content.trim():on(e.content))}const rn=new WeakMap,sn=(e,t)=>function(){if(1!==(e=t.currentNode).type||0!==e.tagType&&1!==e.tagType)return;const{tag:n,props:o}=e,r=1===e.tagType;let s=r?cn(e,t):`"${n}"`;let i,c,l,a,p,u,f=0,d=m(s)&&s.callee===B||s===I||s===k||!r&&("svg"===n||"foreignObject"===n||we(e,"key",!0));if(o.length>0){const n=ln(e,t);i=n.props,f=n.patchFlag,p=n.dynamicPropNames;const o=n.directives;u=o&&o.length?pe(o.map((e=>function(e,t){const n=[],o=rn.get(e);o?n.push(t.helperString(o)):(t.helper(F),t.directives.add(e.name),n.push(Fe(e.name,"directive")));const{loc:r}=e;e.exp&&n.push(e.exp);e.arg&&(e.exp||n.push("void 0"),n.push(e.arg));if(Object.keys(e.modifiers).length){e.arg||(e.exp||n.push("void 0"),n.push("void 0"));const t=de("true",!1,r);n.push(ue(e.modifiers.map((e=>fe(e,t))),r))}return pe(n,e.loc)}(e,t)))):void 0}if(e.children.length>0){s===M&&(d=!0,f|=1024);if(r&&s!==I&&s!==M){const{slots:n,hasDynamicSlots:o}=en(e,t);c=n,o&&(f|=1024)}else if(1===e.children.length&&s!==I){const n=e.children[0],o=n.type,r=5===o||8===o;r&&0===St(n,t)&&(f|=1),c=r||2===o?n:e.children}else c=e.children}0!==f&&(l=String(f),p&&p.length&&(a=function(e){let t="[";for(let n=0,o=e.length;n<o;n++)t+=JSON.stringify(e[n]),n<o-1&&(t+=", ");return t+"]"}(p))),e.codegenNode=ae(t,s,i,c,l,a,u,!!d,!1,e.loc)};function cn(e,t,n=!1){let{tag:o}=e;const r=un(o),s=we(e,"is");if(s)if(r||We("COMPILER_IS_ON_ELEMENT",t)){const e=6===s.type?s.value&&de(s.value.content,!0):s.exp;if(e)return me(t.helper(B),[e])}else 6===s.type&&s.value.content.startsWith("vue:")&&(o=s.value.content.slice(4));const i=!r&&Pe(e,"is");if(i&&i.exp)return me(t.helper(B),[i.exp]);const c=Ee(o)||t.isBuiltInComponent(o);return c?(n||t.helper(c),c):(t.helper(D),t.components.add(o),Fe(o,"component"))}function ln(e,t,n=e.props,o=!1){const{tag:r,loc:s}=e,i=1===e.tagType;let c=[];const l=[],a=[];let u=0,f=!1,d=!1,m=!1,y=!1,v=!1,b=!1;const S=[],E=({key:e,value:n})=>{if(be(e)){const o=e.content,r=(e=>p.test(e))(o);if(i||!r||"onclick"===o.toLowerCase()||"onUpdate:modelValue"===o||g(o)||(y=!0),r&&g(o)&&(b=!0),20===n.type||(4===n.type||8===n.type)&&St(n,t)>0)return;"ref"===o?f=!0:"class"!==o||i?"style"!==o||i?"key"===o||S.includes(o)||S.push(o):m=!0:d=!0}else v=!0};for(let p=0;p<n.length;p++){const i=n[p];if(6===i.type){const{loc:e,name:n,value:o}=i;let s=!0;if("ref"===n&&(f=!0),"is"===n&&(un(r)||o&&o.content.startsWith("vue:")||We("COMPILER_IS_ON_ELEMENT",t)))continue;c.push(fe(de(n,!0,Ie(e,0,n.length)),de(o?o.content:"",s,o?o.loc:e)))}else{const{name:n,arg:p,exp:u,loc:f}=i,d="bind"===n,m="on"===n;if("slot"===n)continue;if("once"===n)continue;if("is"===n||d&&Re(p,"is")&&(un(r)||We("COMPILER_IS_ON_ELEMENT",t)))continue;if(m&&o)continue;if(!p&&(d||m)){if(v=!0,u)if(c.length&&(l.push(ue(an(c),s)),c=[]),d){if(We("COMPILER_V_BIND_OBJECT_ORDER",t)){l.unshift(u);continue}l.push(u)}else l.push({type:14,loc:f,callee:t.helper(z),arguments:[u]});continue}const g=t.directiveTransforms[n];if(g){const{props:n,needRuntime:r}=g(i,e,t);!o&&n.forEach(E),c.push(...n),r&&(a.push(i),h(r)&&rn.set(i,r))}else a.push(i)}6===i.type&&"ref"===i.name&&t.scopes.vFor>0&&Ke("COMPILER_V_FOR_REF",t)&&c.push(fe(de("refInFor",!0),de("true",!1)))}let T;return l.length?(c.length&&l.push(ue(an(c),s)),T=l.length>1?me(t.helper(G),l,s):l[0]):c.length&&(T=ue(an(c),s)),v?u|=16:(d&&(u|=2),m&&(u|=4),S.length&&(u|=8),y&&(u|=32)),0!==u&&32!==u||!(f||b||a.length>0)||(u|=512),{props:T,directives:a,patchFlag:u,dynamicPropNames:S}}function an(e){const t=new Map,n=[];for(let o=0;o<e.length;o++){const r=e[o];if(8===r.key.type||!r.key.isStatic){n.push(r);continue}const s=r.key.content,i=t.get(s);i?("style"===s||"class"===s||s.startsWith("on"))&&pn(i,r):(t.set(s,r),n.push(r))}return n}function pn(e,t){17===e.value.type?e.value.elements.push(t.value):e.value=pe([e.value,t.value],e.loc)}function un(e){return e[0].toLowerCase()+e.slice(1)==="component"}const fn=(e,t)=>{if(De(e)){const{children:n,loc:o}=e,{slotName:r,slotProps:s}=dn(e,t),i=[t.prefixIdentifiers?"_ctx.$slots":"$slots",r];s&&i.push(s),n.length&&(s||i.push("{}"),i.push(ge([],n,!1,!1,o))),t.scopeId&&!t.slotted&&(s||i.push("{}"),n.length||i.push("undefined"),i.push("true")),e.codegenNode=me(t.helper(K),i,o)}};function dn(e,t){let n,o='"default"';const r=[];for(let s=0;s<e.props.length;s++){const t=e.props[s];6===t.type?t.value&&("name"===t.name?o=JSON.stringify(t.value.content):(t.name=b(t.name),r.push(t))):"bind"===t.name&&Re(t.arg,"name")?t.exp&&(o=t.exp):("bind"===t.name&&t.arg&&be(t.arg)&&(t.arg.content=b(t.arg.content)),r.push(t))}if(r.length>0){const{props:o,directives:s}=ln(e,t,r);n=o}return{slotName:o,slotProps:n}}const hn=/^\s*([\w$_]+|\([^)]*?\))\s*=>|^\s*function(?:\s+[\w$]+)?\s*\(/,mn=(e,t,n,o)=>{const{loc:r,modifiers:s,arg:i}=e;let c;if(4===i.type)if(i.isStatic){c=de(x(b(i.content)),!0,i.loc)}else c=he([`${n.helperString(Z)}(`,i,")"]);else c=i,c.children.unshift(`${n.helperString(Z)}(`),c.children.push(")");let l=e.exp;l&&!l.content.trim()&&(l=void 0);let a=n.cacheHandlers&&!l;if(l){const e=Ce(l.content),t=!(e||hn.test(l.content)),n=l.content.includes(";");(t||a&&e)&&(l=he([`${t?"$event":"(...args)"} => ${n?"{":"("}`,l,n?"}":")"]))}let p={props:[fe(c,l||de("() => {}",!1,r))]};return o&&(p=o(p)),a&&(p.props[0].value=n.cache(p.props[0].value)),p},gn=(e,t,n)=>{const{exp:o,modifiers:r,loc:s}=e,i=e.arg;return 4!==i.type?(i.children.unshift("("),i.children.push(') || ""')):i.isStatic||(i.content=`${i.content} || ""`),r.includes("camel")&&(4===i.type?i.content=i.isStatic?b(i.content):`${n.helperString(q)}(${i.content})`:(i.children.unshift(`${n.helperString(q)}(`),i.children.push(")"))),!o||4===o.type&&!o.content.trim()?{props:[fe(i,de("",!0,s))]}:{props:[fe(i,o)]}},yn=(e,t)=>{if(0===e.type||1===e.type||11===e.type||10===e.type)return()=>{const n=e.children;let o,r=!1;for(let e=0;e<n.length;e++){const t=n[e];if(Le(t)){r=!0;for(let r=e+1;r<n.length;r++){const s=n[r];if(!Le(s)){o=void 0;break}o||(o=n[e]={type:8,loc:t.loc,children:[t]}),o.children.push(" + ",s),n.splice(r,1),r--}}}if(r&&(1!==n.length||0!==e.type&&(1!==e.type||0!==e.tagType||e.props.find((e=>7===e.type&&!t.directiveTransforms[e.name]))||"template"===e.tag)))for(let e=0;e<n.length;e++){const o=n[e];if(Le(o)||8===o.type){const r=[];2===o.type&&" "===o.content||r.push(o),t.ssr||0!==St(o,t)||r.push("1"),n[e]={type:12,content:o,loc:o.loc,codegenNode:me(t.helper(V),r)}}}}},vn=new WeakSet,bn=(e,t)=>{if(1===e.type&&Pe(e,"once",!0)){if(vn.has(e))return;return vn.add(e),t.helper(X),()=>{const e=t.currentNode;e.codegenNode&&(e.codegenNode=t.cache(e.codegenNode,!0))}}},Sn=(e,t,n)=>{const{exp:o,arg:r}=e;if(!o)return En();const s=o.loc.source,i=4===o.type?o.content:s;if(!i.trim()||!Ce(i))return En();const c=r||de("modelValue",!0),l=r?be(r)?`onUpdate:${r.content}`:he(['"onUpdate:" + ',r]):"onUpdate:modelValue";let a;a=he([`${n.isTS?"($event: any)":"$event"} => (`,o," = $event)"]);const p=[fe(c,e.exp),fe(l,a)];if(e.modifiers.length&&1===t.tagType){const t=e.modifiers.map((e=>(xe(e)?e:JSON.stringify(e))+": true")).join(", "),n=r?be(r)?`${r.content}Modifiers`:he([r,' + "Modifiers"']):"modelModifiers";p.push(fe(n,de(`{ ${t} }`,!1,e.loc,2)))}return En(p)};function En(e=[]){return{props:e}}const Tn=/[\w).+\-_$\]]/,xn=(e,t)=>{We("COMPILER_FILTER",t)&&(5===e.type&&_n(e.content,t),1===e.type&&e.props.forEach((e=>{7===e.type&&"for"!==e.name&&e.exp&&_n(e.exp,t)})))};function _n(e,t){if(4===e.type)Nn(e,t);else for(let n=0;n<e.children.length;n++){const o=e.children[n];"object"==typeof o&&(4===o.type?Nn(o,t):8===o.type?_n(e,t):5===o.type&&_n(o.content,t))}}function Nn(e,t){const n=e.content;let o,r,s,i,c=!1,l=!1,a=!1,p=!1,u=0,f=0,d=0,h=0,m=[];for(s=0;s<n.length;s++)if(r=o,o=n.charCodeAt(s),c)39===o&&92!==r&&(c=!1);else if(l)34===o&&92!==r&&(l=!1);else if(a)96===o&&92!==r&&(a=!1);else if(p)47===o&&92!==r&&(p=!1);else if(124!==o||124===n.charCodeAt(s+1)||124===n.charCodeAt(s-1)||u||f||d){switch(o){case 34:l=!0;break;case 39:c=!0;break;case 96:a=!0;break;case 40:d++;break;case 41:d--;break;case 91:f++;break;case 93:f--;break;case 123:u++;break;case 125:u--}if(47===o){let e,t=s-1;for(;t>=0&&(e=n.charAt(t)," "===e);t--);e&&Tn.test(e)||(p=!0)}}else void 0===i?(h=s+1,i=n.slice(0,s).trim()):g();function g(){m.push(n.slice(h,s).trim()),h=s+1}if(void 0===i?i=n.slice(0,s).trim():0!==h&&g(),m.length){for(s=0;s<m.length;s++)i=On(i,m[s],t);e.content=i}}function On(e,t,n){n.helper(j);const o=t.indexOf("(");if(o<0)return n.filters.add(t),`${Fe(t,"filter")}(${e})`;{const r=t.slice(0,o),s=t.slice(o+1);return n.filters.add(r),`${Fe(r,"filter")}(${e}${")"!==s?","+s:s}`}}function Cn(e){return[[bn,Dt,Wt,xn,fn,sn,Xt,yn],{on:mn,bind:gn,model:Sn}]}function In(e,t={}){const n=t.onError||_,o="module"===t.mode;!0===t.prefixIdentifiers?n(O(45)):o&&n(O(46));t.cacheHandlers&&n(O(47)),t.scopeId&&!o&&n(O(48));const r=d(e)?ze(e,t):e,[s,i]=Cn();return Nt(r,u({},t,{prefixIdentifiers:false,nodeTransforms:[...s,...t.nodeTransforms||[]],directiveTransforms:u({},i,t.directiveTransforms||{})})),kt(r,u({},t,{prefixIdentifiers:false}))}const kn=()=>({props:[]}),Mn=Symbol(""),Pn=Symbol(""),wn=Symbol(""),Rn=Symbol(""),$n=Symbol(""),Ln=Symbol(""),Vn=Symbol(""),An=Symbol(""),Dn=Symbol(""),Bn=Symbol("");let Fn;ie({[Mn]:"vModelRadio",[Pn]:"vModelCheckbox",[wn]:"vModelText",[Rn]:"vModelSelect",[$n]:"vModelDynamic",[Ln]:"withModifiers",[Vn]:"withKeys",[An]:"vShow",[Dn]:"Transition",[Bn]:"TransitionGroup"});const jn=t("style,iframe,script,noscript",!0),Hn={isVoidTag:i,isNativeTag:e=>r(e)||s(e),isPreTag:e=>"pre"===e,decodeEntities:function(e,t=!1){return Fn||(Fn=document.createElement("div")),t?(Fn.innerHTML=`<div foo="${e.replace(/"/g,"&quot;")}">`,Fn.children[0].getAttribute("foo")):(Fn.innerHTML=e,Fn.textContent)},isBuiltInComponent:e=>Se(e,"Transition")?Dn:Se(e,"TransitionGroup")?Bn:void 0,getNamespace(e,t){let n=t?t.ns:0;if(t&&2===n)if("annotation-xml"===t.tag){if("svg"===e)return 1;t.props.some((e=>6===e.type&&"encoding"===e.name&&null!=e.value&&("text/html"===e.value.content||"application/xhtml+xml"===e.value.content)))&&(n=0)}else/^m(?:[ions]|text)$/.test(t.tag)&&"mglyph"!==e&&"malignmark"!==e&&(n=0);else t&&1===n&&("foreignObject"!==t.tag&&"desc"!==t.tag&&"title"!==t.tag||(n=0));if(0===n){if("svg"===e)return 1;if("math"===e)return 2}return n},getTextMode({tag:e,ns:t}){if(0===t){if("textarea"===e||"title"===e)return 1;if(jn(e))return 2}return 0}},Wn=e=>{1===e.type&&e.props.forEach(((t,n)=>{6===t.type&&"style"===t.name&&t.value&&(e.props[n]={type:7,name:"bind",arg:de("style",!0,t.loc),exp:Kn(t.value.content,t.loc),modifiers:[],loc:t.loc})}))},Kn=(e,t)=>{const r=function(e){const t={};return e.split(n).forEach((e=>{if(e){const n=e.split(o);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}(e);return de(JSON.stringify(r),!1,t,3)};function Un(e,t){return O(e,t)}const Jn=t("passive,once,capture"),Gn=t("stop,prevent,self,ctrl,shift,alt,meta,exact,middle"),zn=t("left,right"),qn=t("onkeyup,onkeydown,onkeypress",!0),Yn=(e,t)=>be(e)&&"onclick"===e.content.toLowerCase()?de(t,!0):4!==e.type?he(["(",e,`) === "onClick" ? "${t}" : (`,e,")"]):e,Zn=(e,t)=>{1!==e.type||0!==e.tagType||"script"!==e.tag&&"style"!==e.tag||t.removeNode()},Xn=[Wn],Qn={cloak:kn,html:(e,t,n)=>{const{exp:o,loc:r}=e;return t.children.length&&(t.children.length=0),{props:[fe(de("innerHTML",!0,r),o||de("",!0))]}},text:(e,t,n)=>{const{exp:o,loc:r}=e;return t.children.length&&(t.children.length=0),{props:[fe(de("textContent",!0),o?me(n.helperString(J),[o],r):de("",!0))]}},model:(e,t,n)=>{const o=Sn(e,t,n);if(!o.props.length||1===t.tagType)return o;const{tag:r}=t,s=n.isCustomElement(r);if("input"===r||"textarea"===r||"select"===r||s){let e=wn,i=!1;if("input"===r||s){const n=we(t,"type");if(n){if(7===n.type)e=$n;else if(n.value)switch(n.value.content){case"radio":e=Mn;break;case"checkbox":e=Pn;break;case"file":i=!0}}else $e(t)&&(e=$n)}else"select"===r&&(e=Rn);i||(o.needRuntime=n.helper(e))}return o.props=o.props.filter((e=>!(4===e.key.type&&"modelValue"===e.key.content))),o},on:(e,t,n)=>mn(e,0,n,(t=>{const{modifiers:o}=e;if(!o.length)return t;let{key:r,value:s}=t.props[0];const{keyModifiers:i,nonKeyModifiers:c,eventOptionModifiers:l}=((e,t,n,o)=>{const r=[],s=[],i=[];for(let c=0;c<t.length;c++){const o=t[c];"native"===o&&Ke("COMPILER_V_ON_NATIVE",n)||Jn(o)?i.push(o):zn(o)?be(e)?qn(e.content)?r.push(o):s.push(o):(r.push(o),s.push(o)):Gn(o)?s.push(o):r.push(o)}return{keyModifiers:r,nonKeyModifiers:s,eventOptionModifiers:i}})(r,o,n);if(c.includes("right")&&(r=Yn(r,"onContextmenu")),c.includes("middle")&&(r=Yn(r,"onMouseup")),c.length&&(s=me(n.helper(Ln),[s,JSON.stringify(c)])),!i.length||be(r)&&!qn(r.content)||(s=me(n.helper(Vn),[s,JSON.stringify(i)])),l.length){const e=l.map(T).join("");r=be(r)?de(`${r.content}${e}`,!0):he(["(",r,`) + "${e}"`])}return{props:[fe(r,s)]}})),show:(e,t,n)=>({props:[],needRuntime:n.helper(An)})};return e.BASE_TRANSITION=P,e.CAMELIZE=q,e.CAPITALIZE=Y,e.CREATE_BLOCK=R,e.CREATE_COMMENT=L,e.CREATE_SLOTS=U,e.CREATE_STATIC=A,e.CREATE_TEXT=V,e.CREATE_VNODE=$,e.DOMDirectiveTransforms=Qn,e.DOMNodeTransforms=Xn,e.FRAGMENT=C,e.IS_REF=re,e.KEEP_ALIVE=M,e.MERGE_PROPS=G,e.OPEN_BLOCK=w,e.POP_SCOPE_ID=ee,e.PUSH_SCOPE_ID=Q,e.RENDER_LIST=W,e.RENDER_SLOT=K,e.RESOLVE_COMPONENT=D,e.RESOLVE_DIRECTIVE=F,e.RESOLVE_DYNAMIC_COMPONENT=B,e.RESOLVE_FILTER=j,e.SET_BLOCK_TRACKING=X,e.SUSPENSE=k,e.TELEPORT=I,e.TO_DISPLAY_STRING=J,e.TO_HANDLERS=z,e.TO_HANDLER_KEY=Z,e.TRANSITION=Dn,e.TRANSITION_GROUP=Bn,e.UNREF=oe,e.V_MODEL_CHECKBOX=Pn,e.V_MODEL_DYNAMIC=$n,e.V_MODEL_RADIO=Mn,e.V_MODEL_SELECT=Rn,e.V_MODEL_TEXT=wn,e.V_ON_WITH_KEYS=Vn,e.V_ON_WITH_MODIFIERS=Ln,e.V_SHOW=An,e.WITH_CTX=ne,e.WITH_DIRECTIVES=H,e.WITH_SCOPE_ID=te,e.advancePositionWithClone=ke,e.advancePositionWithMutation=Me,e.assert=function(e,t){if(!e)throw new Error(t||"unexpected compiler condition")},e.baseCompile=In,e.baseParse=ze,e.buildProps=ln,e.buildSlots=en,e.checkCompatEnabled=Ke,e.compile=function(e,t={}){return In(e,u({},Hn,t,{nodeTransforms:[Zn,...Xn,...t.nodeTransforms||[]],directiveTransforms:u({},Qn,t.directiveTransforms||{}),transformHoist:null}))},e.createArrayExpression=pe,e.createAssignmentExpression=function(e,t){return{type:24,left:e,right:t,loc:ce}},e.createBlockStatement=function(e){return{type:21,body:e,loc:ce}},e.createCacheExpression=ve,e.createCallExpression=me,e.createCompilerError=O,e.createCompoundExpression=he,e.createConditionalExpression=ye,e.createDOMCompilerError=Un,e.createForLoopParams=Yt,e.createFunctionExpression=ge,e.createIfStatement=function(e,t,n){return{type:23,test:e,consequent:t,alternate:n,loc:ce}},e.createInterpolation=function(e,t){return{type:5,loc:t,content:d(e)?de(e,!1,t):e}},e.createObjectExpression=ue,e.createObjectProperty=fe,e.createReturnStatement=function(e){return{type:26,returns:e,loc:ce}},e.createRoot=le,e.createSequenceExpression=function(e){return{type:25,expressions:e,loc:ce}},e.createSimpleExpression=de,e.createStructuralDirectiveTransform=Ct,e.createTemplateLiteral=function(e){return{type:22,elements:e,loc:ce}},e.createTransformContext=_t,e.createVNodeCall=ae,e.findDir=Pe,e.findProp=we,e.generate=kt,e.generateCodeFrame=function(e,t=0,n=e.length){const o=e.split(/\r?\n/);let r=0;const s=[];for(let i=0;i<o.length;i++)if(r+=o[i].length+1,r>=t){for(let e=i-2;e<=i+2||n>r;e++){if(e<0||e>=o.length)continue;const c=e+1;s.push(`${c}${" ".repeat(Math.max(3-String(c).length,0))}|  ${o[e]}`);const l=o[e].length;if(e===i){const e=t-(r-l)+1,o=Math.max(1,n>r?l-e:n-t);s.push("   |  "+" ".repeat(e)+"^".repeat(o))}else if(e>i){if(n>r){const e=Math.max(Math.min(n-r,l),1);s.push("   |  "+"^".repeat(e))}r+=l+1}}break}return s.join("\n")},e.getBaseTransformPreset=Cn,e.getInnerRange=Ie,e.hasDynamicKeyVBind=$e,e.hasScopeRef=function e(t,n){if(!t||0===Object.keys(n).length)return!1;switch(t.type){case 1:for(let o=0;o<t.props.length;o++){const r=t.props[o];if(7===r.type&&(e(r.arg,n)||e(r.exp,n)))return!0}return t.children.some((t=>e(t,n)));case 11:return!!e(t.source,n)||t.children.some((t=>e(t,n)));case 9:return t.branches.some((t=>e(t,n)));case 10:return!!e(t.condition,n)||t.children.some((t=>e(t,n)));case 4:return!t.isStatic&&xe(t.content)&&!!n[t.content];case 8:return t.children.some((t=>m(t)&&e(t,n)));case 5:case 12:return e(t.content,n);case 2:case 3:default:return!1}},e.helperNameMap=se,e.injectProp=Be,e.isBindKey=Re,e.isBuiltInType=Se,e.isCoreComponent=Ee,e.isMemberExpression=Ce,e.isSimpleIdentifier=xe,e.isSlotOutlet=De,e.isStaticExp=be,e.isTemplateNode=Ae,e.isText=Le,e.isVSlot=Ve,e.locStub=ce,e.noopDirectiveTransform=kn,e.parse=function(e,t={}){return ze(e,u({},Hn,t))},e.parserOptions=Hn,e.processExpression=At,e.processFor=Kt,e.processIf=Bt,e.processSlotOutlet=dn,e.registerRuntimeHelpers=ie,e.resolveComponentType=cn,e.toValidAssetId=Fe,e.trackSlotScopes=Xt,e.trackVForSlotScopes=(e,t)=>{let n;if(Ae(e)&&e.props.some(Ve)&&(n=Pe(e,"for"))){const e=n.parseResult=zt(n.exp);if(e){const{value:n,key:o,index:r}=e,{addIdentifiers:s,removeIdentifiers:i}=t;return n&&s(n),o&&s(o),r&&s(r),()=>{n&&i(n),o&&i(o),r&&i(r)}}}},e.transform=Nt,e.transformBind=gn,e.transformElement=sn,e.transformExpression=(e,t)=>{if(5===e.type)e.content=At(e.content);else if(1===e.type)for(let n=0;n<e.props.length;n++){const o=e.props[n];if(7===o.type&&"for"!==o.name){const e=o.exp,n=o.arg;!e||4!==e.type||"on"===o.name&&n||(o.exp=At(e,t,"slot"===o.name)),n&&4===n.type&&!n.isStatic&&(o.arg=At(n))}}},e.transformModel=Sn,e.transformOn=mn,e.transformStyle=Wn,e.traverseNode=Ot,e.warnDeprecation=function(e,t,n,...o){if("suppress-warning"===He(e,t))return;const{message:r,link:s}=je[e],i=`(deprecation ${e}) ${"function"==typeof r?r(...o):r}${s?`\n  Details: ${s}`:""}`,c=new SyntaxError(i);c.code=e,n&&(c.loc=n),t.onWarn(c)},Object.defineProperty(e,"__esModule",{value:!0}),e}({});
