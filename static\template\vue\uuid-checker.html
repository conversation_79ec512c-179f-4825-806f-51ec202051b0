<div class="uuid-checker">
    <div class="header">
        <h2>UUID 檢查工具</h2>
        <ui-button 
            class="blue check-btn" 
            @click="startCheck" 
            :disabled="isChecking">
            {{ isChecking ? '檢查中...' : '開始檢查' }}
        </ui-button>
    </div>
    
    <div v-if="isChecking" class="progress">
        <ui-progress :value="progress" :max="100"></ui-progress>
        <p>{{ progressText }}</p>
    </div>
    
    <div v-if="checkResult" class="results">
        <div class="summary">
            <h3>檢查結果摘要</h3>
            <p>總檔案數: {{ checkResult.totalFiles }}</p>
            <p>已使用的UUID: {{ checkResult.usedUuids.length }}</p>
            <p>未使用的UUID: {{ checkResult.unusedUuids.length }}</p>
        </div>
        
        <div v-if="checkResult.unusedUuids.length > 0" class="unused-section">
            <h3>未使用的UUID ({{ checkResult.unusedUuids.length }})</h3>
            <div class="unused-list">
                <div v-for="item in checkResult.unusedUuids" :key="item.uuid" class="unused-item">
                    <div class="uuid-info">
                        <strong>UUID:</strong> {{ item.uuid }}
                    </div>
                    <div class="file-info">
                        <strong>檔案:</strong> {{ item.path }}
                    </div>
                    <div class="asset-info">
                        <strong>資源名稱:</strong> {{ item.name }}
                        <span class="asset-type">({{ item.type }})</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="used-section">
            <h3>已使用的UUID ({{ checkResult.usedUuids.length }})</h3>
            <ui-button @click="toggleUsedList" class="toggle-btn">
                {{ showUsedList ? '隱藏' : '顯示' }}已使用列表
            </ui-button>
            <div v-if="showUsedList" class="used-list">
                <div v-for="item in checkResult.usedUuids" :key="item.uuid" class="used-item">
                    <div class="uuid-info">
                        <strong>UUID:</strong> {{ item.uuid }}
                    </div>
                    <div class="file-info">
                        <strong>檔案:</strong> {{ item.path }}
                    </div>
                    <div class="asset-info">
                        <strong>資源名稱:</strong> {{ item.name }}
                        <span class="asset-type">({{ item.type }})</span>
                    </div>
                    <div class="references">
                        <strong>引用位置:</strong>
                        <ul>
                            <li v-for="ref in item.references" :key="ref">{{ ref }}</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div v-if="error" class="error">
        <h3>錯誤</h3>
        <p>{{ error }}</p>
    </div>
</div>
