# UUID檢查工具使用說明

## 功能概述

這個工具可以幫助您檢查Cocos Creator專案中的UUID使用情況，找出未被使用的資源檔案。

## 使用步驟

### 1. 啟用擴展
在Cocos Creator中啟用此擴展：
- 打開 `擴展 -> 擴展管理器`
- 找到並啟用 `uuid_tools` 擴展

### 2. 打開檢查面板
- 點擊主菜單 `面板 -> uuid_tools -> UUID檢查工具`
- 面板將在編輯器中打開

### 3. 執行檢查
- 點擊面板中的「開始檢查」按鈕
- 工具將開始掃描專案中的所有資源檔案
- 檢查過程中會顯示進度條和當前狀態

### 4. 查看結果
檢查完成後，面板會顯示：

#### 摘要信息
- 總檔案數：掃描到的資源檔案總數
- 已使用的UUID：被其他檔案引用的UUID數量
- 未使用的UUID：沒有被引用的UUID數量

#### 未使用的UUID列表
- 顯示所有未被使用的UUID
- 包含UUID值、檔案路徑、資源名稱和類型
- 這些資源可能是可以清理的無用檔案

#### 已使用的UUID列表
- 點擊「顯示已使用列表」可以查看
- 顯示所有被使用的UUID及其引用位置
- 幫助了解資源的依賴關係

## 檢查範圍

工具會搜尋以下類型的檔案來查找UUID引用：
- `.scene` - 場景檔案
- `.prefab` - 預製體檔案
- `.ts` - TypeScript檔案
- `.js` - JavaScript檔案
- `.json` - JSON配置檔案
- `.meta` - 資源元數據檔案

## 注意事項

1. **檢查時間**：檢查時間取決於專案大小，大型專案可能需要較長時間
2. **系統目錄**：工具會自動跳過系統目錄（node_modules、.git、library、local、temp）
3. **備份建議**：在刪除未使用的資源前，建議先備份專案
4. **手動驗證**：建議在刪除資源前手動驗證是否真的不需要

## 故障排除

### 檢查失敗
如果檢查過程中出現錯誤：
1. 檢查控制台是否有錯誤信息
2. 確保專案路徑正確
3. 檢查是否有檔案權限問題

### 結果不準確
如果結果看起來不正確：
1. 確保專案已完全載入
2. 檢查是否有動態載入的資源
3. 考慮代碼中的字符串拼接引用

## 技術細節

- 基於Vue 3.x開發
- 使用Cocos Creator的asset-db API
- 支援即時進度顯示
- 響應式UI設計
